package com.fsti.myapplication.ui

import android.app.Application
import android.graphics.Bitmap
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.fsti.myapplication.network.VisionApiClient
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

// 定义UI状态
enum class UiState {
    IDLE,       // 空闲
    RECORDING,  // 正在录音
    PROCESSING  // 正在处理（语音识别或AI响应）
}

// 定义聊天消息的数据类
data class ChatMessage(
    val message: String,
    val isUser: Boolean
)

class MainViewModel(application: Application) : AndroidViewModel(application) {

    private val _uiState = MutableStateFlow(UiState.IDLE)
    val uiState: StateFlow<UiState> = _uiState.asStateFlow()

    private val _messages = MutableStateFlow<List<ChatMessage>>(emptyList())
    val messages: StateFlow<List<ChatMessage>> = _messages.asStateFlow()

    private val speechManager = SpeechRecognitionManager(application)
    private val ttsManager = TtsManager(application)

    private var capturedBitmap: Bitmap? = null

    // TTS状态
    val isTtsSpeaking: StateFlow<Boolean> = ttsManager.isSpeaking
    val ttsStatus: StateFlow<TtsManager.TtsStatus> = ttsManager.ttsStatus

    init {
        addAssistantMessage("您好！我是您的智能视觉助手，按住麦克风按钮说话，我会根据摄像头看到的内容回答您的问题。")
        
        viewModelScope.launch {
            speechManager.speechState.collect { state ->
                when (state) {
                    is SpeechRecognitionManager.SpeechState.Success -> {
                        val userText = state.text
                        addUserMessage(userText)
                        val bitmap = capturedBitmap
                        if (bitmap != null) {
                            sendVisionRequest(userText, bitmap)
                        } else {
                            addAssistantMessage("错误：未捕获到图像。")
                            processingComplete()
                        }
                    }
                    is SpeechRecognitionManager.SpeechState.Error -> {
                        addAssistantMessage("语音识别错误: ${state.message}")
                        processingComplete()
                    }
                    SpeechRecognitionManager.SpeechState.Idle -> { }
                }
            }
        }
    }
    
    private fun sendVisionRequest(text: String, bitmap: Bitmap) {
        VisionApiClient.getVisionResponse(text, bitmap) { result ->
            viewModelScope.launch(Dispatchers.Main) {
                result.fold(
                    onSuccess = { responseText ->
                        addAssistantMessage(responseText)
                        ttsManager.speak(responseText)
                    },
                    onFailure = { error ->
                        addAssistantMessage("API请求失败: ${error.message}")
                    }
                )
                processingComplete()
            }
        }
    }

    fun startRecording() {
        _uiState.value = UiState.RECORDING
        speechManager.startListening()
    }

    fun stopRecording() {
        _uiState.value = UiState.PROCESSING
        speechManager.stopListening()
    }

    private fun processingComplete() {
        _uiState.value = UiState.IDLE
        capturedBitmap = null
    }

    fun addUserMessage(text: String) {
        val newMessages = _messages.value.toMutableList().apply {
            add(ChatMessage(text, true))
        }
        _messages.value = newMessages
    }

    fun addAssistantMessage(text: String) {
        val newMessages = _messages.value.toMutableList().apply {
            add(ChatMessage(text, false))
        }
        _messages.value = newMessages
    }

    fun onPictureTaken(bitmap: Bitmap) {
        this.capturedBitmap = bitmap
    }

    /**
     * 停止TTS播放
     */
    fun stopTts() {
        ttsManager.stop()
    }

    /**
     * 检查TTS是否正在播放
     */
    fun isTtsPlaying(): Boolean {
        return ttsManager.isTtsSpeaking()
    }

    override fun onCleared() {
        super.onCleared()
        speechManager.destroy()
        ttsManager.shutdown()
    }
} 