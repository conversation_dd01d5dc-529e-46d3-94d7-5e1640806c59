<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#eaeaea">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:orientation="vertical">

        <EditText
            android:id="@+id/ai_tts_input"
            android:layout_width="match_parent"
            android:layout_height="150dp"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:text="@string/TTS_TEXT"/>



        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:gravity="center_vertical"
            android:layout_marginLeft="20dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/ai_tts_vcn"
                android:layout_width="70dp"
                android:layout_height="wrap_content"
                android:text="发音人："
                android:textColor="#333333"
                android:textSize="16sp" />

            <Spinner
                android:id="@+id/ai_tts_vcn_spinner"
                android:layout_width="wrap_content"
                android:layout_height="50dp"
                android:layout_marginLeft="10dp"/>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:gravity="center_vertical"
            android:layout_marginLeft="20dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/ai_tts_pitch"
                android:layout_width="70dp"
                android:layout_height="wrap_content"
                android:text="语调："
                android:textColor="#333333"
                android:textSize="16sp" />

            <SeekBar
                android:id="@+id/ai_tts_pitch_seekbar"
                android:layout_width="200dp"
                android:layout_height="wrap_content"
                android:max="100"
                android:progress="50"
                android:progressBackgroundTint="@color/purple_500"
                android:layout_marginLeft="10dp"/>

            <TextView
                android:id="@+id/ai_tts_pitch_txt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#333333"
                android:textSize="16sp"
                android:layout_marginLeft="5dp"
                android:text="50"/>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:gravity="center_vertical"
            android:layout_marginLeft="20dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/ai_tts_speed"
                android:layout_width="70dp"
                android:layout_height="wrap_content"
                android:text="语速："
                android:textColor="#333333"
                android:textSize="16sp" />

            <SeekBar
                android:id="@+id/ai_tts_speed_seekbar"
                android:layout_width="200dp"
                android:layout_height="wrap_content"
                android:max="100"
                android:progress="50"
                android:progressBackgroundTint="@color/purple_500"
                android:layout_marginLeft="10dp"/>

            <TextView
                android:id="@+id/ai_tts_speed_txt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#333333"
                android:textSize="16sp"
                android:layout_marginLeft="5dp"
                android:text="50"/>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:gravity="center_vertical"
            android:layout_marginLeft="20dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/ai_tts_volume"
                android:layout_width="70dp"
                android:layout_height="wrap_content"
                android:text="音量："
                android:textColor="#333333"
                android:textSize="16sp" />

            <SeekBar
                android:id="@+id/ai_tts_volume_seekbar"
                android:layout_width="200dp"
                android:layout_height="wrap_content"
                android:max="100"
                android:progress="50"
                android:progressBackgroundTint="@color/purple_500"
                android:layout_marginLeft="10dp"/>

            <TextView
                android:id="@+id/ai_tts_volume_txt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#333333"
                android:textSize="16sp"
                android:layout_marginLeft="5dp"
                android:text="50"/>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="20dp">

            <Button
                android:id="@+id/ai_tts_play_btn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="18sp"
                android:textColor="#ffffff"
                android:text="合成并播放"/>

            <Button
                android:id="@+id/ai_tts_stop_btn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="18sp"
                android:textColor="#ffffff"
                android:text="停止播报"/>
        </LinearLayout>
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:id = "@+id/ai_tts_notification"
            />

    </LinearLayout>
</ScrollView>