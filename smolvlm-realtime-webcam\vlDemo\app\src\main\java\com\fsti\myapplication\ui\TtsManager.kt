package com.fsti.myapplication.ui

import android.content.Context
import android.speech.tts.TextToSpeech
import android.speech.tts.UtteranceProgressListener
import android.util.Log
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.util.Locale

class TtsManager(context: Context) : TextToSpeech.OnInitListener {

    private lateinit var tts: TextToSpeech
    private var isInitialized = false

    // TTS状态管理
    private val _isSpeaking = MutableStateFlow(false)
    val isSpeaking: StateFlow<Boolean> = _isSpeaking.asStateFlow()

    private val _ttsStatus = MutableStateFlow<TtsStatus>(TtsStatus.Idle)
    val ttsStatus: StateFlow<TtsStatus> = _ttsStatus.asStateFlow()

    // TTS状态枚举
    sealed class TtsStatus {
        object Idle : TtsStatus()
        object Speaking : TtsStatus()
        object Stopped : TtsStatus()
        data class Error(val message: String) : TtsStatus()
    }

    init {
        tts = TextToSpeech(context, this)
        // 设置TTS进度监听器
        tts.setOnUtteranceProgressListener(object : UtteranceProgressListener() {
            override fun onStart(utteranceId: String?) {
                Log.d(TAG, "TTS开始播放: $utteranceId")
                _isSpeaking.value = true
                _ttsStatus.value = TtsStatus.Speaking
            }

            override fun onDone(utteranceId: String?) {
                Log.d(TAG, "TTS播放完成: $utteranceId")
                _isSpeaking.value = false
                _ttsStatus.value = TtsStatus.Idle
            }

            override fun onError(utteranceId: String?) {
                Log.e(TAG, "TTS播放错误: $utteranceId")
                _isSpeaking.value = false
                _ttsStatus.value = TtsStatus.Error("TTS播放失败")
            }

            override fun onStop(utteranceId: String?, interrupted: Boolean) {
                Log.d(TAG, "TTS播放停止: $utteranceId, 被中断: $interrupted")
                _isSpeaking.value = false
                _ttsStatus.value = TtsStatus.Stopped
            }
        })
    }

    override fun onInit(status: Int) {
        if (status == TextToSpeech.SUCCESS) {
            val result = tts.setLanguage(Locale.CHINESE)
            if (result == TextToSpeech.LANG_MISSING_DATA || result == TextToSpeech.LANG_NOT_SUPPORTED) {
                Log.e(TAG, "不支持中文语音合成，尝试使用默认语言")
                tts.setLanguage(Locale.getDefault())
            }

            // 设置语音参数
            tts.setSpeechRate(0.9f) // 语速
            tts.setPitch(1.0f) // 音调

            isInitialized = true
            Log.d(TAG, "TTS初始化成功")
        } else {
            Log.e(TAG, "TTS初始化失败")
            _ttsStatus.value = TtsStatus.Error("TTS初始化失败")
        }
    }

    /**
     * 播放文本语音
     * @param text 要播放的文本
     * @param utteranceId 可选的语音ID，用于跟踪
     */
    fun speak(text: String, utteranceId: String = "tts_${System.currentTimeMillis()}") {
        if (!isInitialized) {
            Log.w(TAG, "TTS未初始化，无法播放语音")
            _ttsStatus.value = TtsStatus.Error("TTS未初始化")
            return
        }

        // 如果正在播放，先停止
        if (_isSpeaking.value) {
            stop()
        }

        val params = hashMapOf<String, String>().apply {
            put(TextToSpeech.Engine.KEY_PARAM_UTTERANCE_ID, utteranceId)
        }

        val result = tts.speak(text, TextToSpeech.QUEUE_FLUSH, params)
        if (result == TextToSpeech.ERROR) {
            Log.e(TAG, "TTS播放失败")
            _ttsStatus.value = TtsStatus.Error("TTS播放失败")
        }
    }

    /**
     * 停止当前的TTS播放
     */
    fun stop() {
        if (isInitialized && _isSpeaking.value) {
            tts.stop()
            Log.d(TAG, "TTS播放已停止")
        }
    }

    /**
     * 检查TTS是否正在播放
     */
    fun isTtsSpeaking(): Boolean = _isSpeaking.value

    /**
     * 关闭TTS服务
     */
    fun shutdown() {
        stop()
        if (isInitialized) {
            tts.shutdown()
            isInitialized = false
        }
        _isSpeaking.value = false
        _ttsStatus.value = TtsStatus.Idle
    }

    companion object {
        private const val TAG = "TtsManager"
    }
}