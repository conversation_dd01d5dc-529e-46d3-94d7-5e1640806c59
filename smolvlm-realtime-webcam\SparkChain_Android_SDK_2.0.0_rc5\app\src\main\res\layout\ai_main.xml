<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    >
    <Button
        android:id="@+id/ai_main_sdk_init"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="SDK初始化"
        />
    <Button
        android:id="@+id/ai_main_its"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="在线翻译" />
    <Button
        android:id="@+id/ai_main_rtasr"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="实时语音转写"
        />
    <Button
        android:id="@+id/ai_main_tts"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="在线合成"
        />
    <Button
        android:id="@+id/ai_main_asr"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="语音听写"
        />
    <Button
        android:id="@+id/ai_main_raasr"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="录音文件转写"
        />
    <TextView
        android:id="@+id/ai_main_notification"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:text="请先初始化SDK" />
</LinearLayout>