package com.fsti.myapplication.network

import android.graphics.Bitmap
import android.util.Base64
import android.util.Log
import com.squareup.moshi.Moshi
import com.squareup.moshi.kotlin.reflect.KotlinJsonAdapterFactory
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.ByteArrayOutputStream
import java.io.IOException
import java.util.concurrent.TimeUnit

object VisionApiClient {
    private const val TAG = "VisionApiClient"

    // 默认API地址，与Web端保持一致
    private const val DEFAULT_API_URL = "http://203.176.94.21:18099/vl"

    private val client = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(60, TimeUnit.SECONDS)
        .writeTimeout(60, TimeUnit.SECONDS)
        .build()

    private val moshi = Moshi.Builder()
        .add(KotlinJsonAdapterFactory())
        .build()

    private val visionRequestAdapter = moshi.adapter(VisionRequest::class.java)
    private val visionResponseAdapter = moshi.adapter(VisionResponse::class.java)

    // 系统提示词 - 与Web端保持一致
    private const val SYSTEM_PROMPT = """# 角色
你是一个专为中国道路交通场景设计的视觉问答AI助手。

# 核心任务
分析用户提供的实时视频帧图像，并根据图像内容，用简短、明确的中文回答用户的问题。

# 行为准则
1.  **回答简短明确**：直接给出核心答案，不要有任何多余的客套话或解释。例如，不要说"根据图像分析，这辆车是..."或"您好，我看到的树是..."。
2.  **聚焦图像事实**：你的回答必须严格基于图像中的可见信息。如果问题无法根据图像回答（例如，物体太远、被遮挡或模糊），直接回答"无法识别"或"信息不足"。
3.  **中国本地化**：优先使用中国大陆的常用称呼。例如，车辆品牌（如：比亚迪、理想、问界）、植物名称（如：银杏、法桐）、交通标志等。

# 示例
---
**示例 1:**
*   [用户问题]: 前面这辆白色的车是什么牌子？
*   [你的回答]: 特斯拉 Model Y。

**示例 2:**
*   [用户问题]: 右边这个是什么树？
*   [你的回答]: 银杏树。

---

# 开始任务
请严格遵循以上指示。现在，分析以下输入并回答。"""

    /**
     * 发送视觉问答请求到后端API
     * @param text 用户的问题文本
     * @param bitmap 摄像头捕获的图像
     * @param apiUrl 可选的API地址，默认使用DEFAULT_API_URL
     * @param callback 结果回调
     */
    fun getVisionResponse(
        text: String,
        bitmap: Bitmap,
        apiUrl: String = DEFAULT_API_URL,
        callback: (Result<String>) -> Unit
    ) {
        try {
            // 将Bitmap转换为Base64编码的Data URL
            val base64Image = bitmapToBase64DataUrl(bitmap)

            // 构建请求体，包含系统提示词和用户消息
            val request = VisionRequest(
                maxTokens = 200,
                messages = listOf(
                    // 系统提示词消息
                    Message(
                        role = "system",
                        content = listOf(
                            ContentPart.TextPart(text = SYSTEM_PROMPT)
                        )
                    ),
                    // 用户消息（文本 + 图像）
                    Message(
                        role = "user",
                        content = listOf(
                            ContentPart.TextPart(text = text),
                            ContentPart.ImagePart(
                                imageUrl = ImageUrl(url = base64Image)
                            )
                        )
                    )
                )
            )

            val requestJson = visionRequestAdapter.toJson(request)
            Log.d(TAG, "Request JSON: $requestJson")

            val requestBody = requestJson.toRequestBody("application/json".toMediaType())

            val httpRequest = Request.Builder()
                .url("$apiUrl/v1/chat/completions")
                .post(requestBody)
                .addHeader("Content-Type", "application/json")
                .build()

            client.newCall(httpRequest).enqueue(object : Callback {
                override fun onFailure(call: Call, e: IOException) {
                    Log.e(TAG, "Network request failed", e)
                    callback(Result.failure(Exception("网络请求失败: ${e.message}")))
                }

                override fun onResponse(call: Call, response: Response) {
                    try {
                        val responseBody = response.body?.string()
                        Log.d(TAG, "Response: $responseBody")

                        if (!response.isSuccessful) {
                            callback(Result.failure(Exception("服务器错误: ${response.code}")))
                            return
                        }

                        if (responseBody.isNullOrEmpty()) {
                            callback(Result.failure(Exception("服务器返回空响应")))
                            return
                        }

                        val visionResponse = visionResponseAdapter.fromJson(responseBody)
                        if (visionResponse?.choices?.isNotEmpty() == true) {
                            val content = visionResponse.choices[0].message.content
                            callback(Result.success(content))
                        } else {
                            callback(Result.failure(Exception("服务器返回格式错误")))
                        }

                    } catch (e: Exception) {
                        Log.e(TAG, "Failed to parse response", e)
                        callback(Result.failure(Exception("解析响应失败: ${e.message}")))
                    }
                }
            })

        } catch (e: Exception) {
            Log.e(TAG, "Failed to create request", e)
            callback(Result.failure(Exception("创建请求失败: ${e.message}")))
        }
    }

    /**
     * 将Bitmap转换为Base64编码的Data URL格式
     * 格式: data:image/jpeg;base64,{base64_encoded_image}
     */
    private fun bitmapToBase64DataUrl(bitmap: Bitmap): String {
        val outputStream = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.JPEG, 80, outputStream)
        val imageBytes = outputStream.toByteArray()
        val base64String = Base64.encodeToString(imageBytes, Base64.NO_WRAP)
        return "data:image/jpeg;base64,$base64String"
    }
}