package com.fsti.myapplication.network

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

// --- Request Models ---

@JsonClass(generateAdapter = true)
data class VisionRequest(
    @Json(name = "max_tokens") val maxTokens: Int = 200,
    @Json(name = "messages") val messages: List<Message>
)

@JsonClass(generateAdapter = true)
data class Message(
    @Json(name = "role") val role: String = "user",
    @<PERSON><PERSON>(name = "content") val content: List<ContentPart>
)

sealed class ContentPart {
    @JsonClass(generateAdapter = true)
    data class TextPart(
        @Json(name = "type") val type: String = "text",
        @Json(name = "text") val text: String
    ) : ContentPart()

    @JsonClass(generateAdapter = true)
    data class ImagePart(
        @Json(name = "type") val type: String = "image_url",
        @Json(name = "image_url") val imageUrl: ImageUrl
    ) : ContentPart()
}

@JsonClass(generateAdapter = true)
data class ImageUrl(
    @Json(name = "url") val url: String
)


// --- Response Models ---

@JsonClass(generateAdapter = true)
data class VisionResponse(
    @Json(name = "choices") val choices: List<Choice>
)

@JsonClass(generateAdapter = true)
data class Choice(
    @Json(name = "message") val message: ResponseMessage
)

@JsonClass(generateAdapter = true)
data class ResponseMessage(
    @Json(name = "content") val content: String
) 