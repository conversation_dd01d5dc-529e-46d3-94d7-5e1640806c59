<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>智能视觉语音助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 10px 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            z-index: 100;
        }

        .header h1 {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .header-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .video-source-switch {
            background: #34C759;
            border: none;
            border-radius: 16px;
            padding: 6px 12px;
            color: white;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .video-source-switch:hover {
            background: #30B550;
        }

        .video-source-switch.camera {
            background: #007AFF;
        }

        .video-source-switch.camera:hover {
            background: #0056D6;
        }

        .file-input-btn {
            background: #FF9500;
            border: none;
            border-radius: 16px;
            padding: 6px 12px;
            color: white;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .file-input-btn:hover {
            background: #E8830C;
        }

        .settings-btn {
            background: #007AFF;
            border: none;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            color: white;
            font-size: 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .video-container {
            position: relative;
            background: #000;
            height: 75vh;
            min-height: 400px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 20px;
            margin: 10px;
            overflow: hidden;
        }

        #videoFeed {
            display: none;
        }

        #displayCanvas {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .camera-status {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.6);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .controls-overlay {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .video-controls {
            position: absolute;
            bottom: 80px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(10px);
            border-radius: 25px;
            padding: 10px 20px;
            display: none;
            align-items: center;
            gap: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .video-controls.show {
            display: flex;
        }

        .play-pause-btn {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 5px;
        }

        .video-progress {
            width: 200px;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            cursor: pointer;
            position: relative;
        }

        .video-progress-bar {
            height: 100%;
            background: #007AFF;
            border-radius: 2px;
            width: 0%;
            transition: width 0.1s ease;
        }

        .video-time {
            color: white;
            font-size: 12px;
            min-width: 80px;
            text-align: center;
        }

        .video-list {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 10px;
            max-width: 250px;
            display: none;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .video-list.show {
            display: block;
        }

        .video-list-item {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 8px 12px;
            margin-bottom: 8px;
            cursor: pointer;
            color: white;
            font-size: 13px;
            transition: all 0.3s ease;
            word-break: break-word;
        }

        .video-list-item:last-child {
            margin-bottom: 0;
        }

        .video-list-item:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .video-list-item.active {
            background: #007AFF;
            border-color: #007AFF;
        }

        .ai-status {
            background: rgba(0, 0, 0, 0.6);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 200px;
            text-align: center;
        }

        .chat-container {
            position: fixed;
            top: 80px;
            right: -350px;
            width: 350px;
            height: calc(100vh - 120px);
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px 0 0 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            z-index: 50;
            transition: right 0.3s ease;
        }

        .chat-container.open {
            right: 0;
        }

        .chat-toggle {
            position: fixed;
            top: 50%;
            right: 10px;
            transform: translateY(-50%);
            width: 40px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 20px 0 0 20px;
            border-right: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 60;
            transition: all 0.3s ease;
        }

        .chat-toggle:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .chat-toggle.open {
            right: 350px;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .message {
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 16px;
            line-height: 1.4;
            word-wrap: break-word;
        }

        .message.user {
            background: #007AFF;
            color: white;
            align-self: flex-end;
            border-bottom-right-radius: 6px;
        }

        .message.assistant {
            background: #F0F0F0;
            color: #333;
            align-self: flex-start;
            border-bottom-left-radius: 6px;
        }

        .message.system {
            background: #FFD60A;
            color: #1D1D1F;
            align-self: center;
            border-radius: 12px;
            font-size: 14px;
            padding: 8px 12px;
        }

        .input-container {
            padding: 15px 20px;
            background: rgba(255, 255, 255, 0.1);
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .voice-btn {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            border: 4px solid white;
            background: #FF3B30;
            color: white;
            font-size: 28px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            flex-shrink: 0;
            box-shadow: 0 4px 20px rgba(255, 59, 48, 0.3);
        }

        .voice-btn:active {
            transform: scale(0.95);
        }

        .voice-btn.idle {
            background: #34C759;
            box-shadow: 0 4px 20px rgba(52, 199, 89, 0.3);
        }

        .voice-btn.recording {
            background: #FF3B30;
            animation: pulse 1s infinite;
            transform: scale(1.1);
            box-shadow: 0 8px 30px rgba(255, 59, 48, 0.5);
        }

        .voice-btn.processing {
            background: #FF9500;
            animation: processingPulse 1.5s infinite;
            box-shadow: 0 4px 20px rgba(255, 149, 0, 0.4);
        }

        .tts-stop-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: 3px solid white;
            background: #FF3B30;
            color: white;
            font-size: 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 59, 48, 0.3);
            margin-left: 10px;
        }

        .tts-stop-btn:hover {
            background: #E5342A;
            transform: scale(1.05);
        }

        .tts-stop-btn:active {
            transform: scale(0.95);
        }

        @keyframes pulse {
            0% { transform: scale(1.1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1.1); }
        }

        @keyframes processingPulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .status-text {
            flex: 1;
            text-align: center;
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            font-weight: 500;
        }

        .settings-panel {
            position: fixed;
            top: 0;
            right: -100%;
            width: 80%;
            max-width: 300px;
            height: 100vh;
            background: white;
            z-index: 200;
            transition: right 0.3s ease;
            padding: 20px;
            overflow-y: auto;
        }

        .settings-panel.open {
            right: 0;
        }

        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 150;
            display: none;
        }

        .overlay.show {
            display: block;
        }

        .setting-group {
            margin-bottom: 20px;
        }

        .setting-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }

        .setting-group input,
        .setting-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
        }

        .close-settings {
            position: absolute;
            top: 15px;
            right: 15px;
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
        }

        /* 隐藏的元素 */
        .hidden {
            display: none;
        }

        /* 移动端优化 */
        @media (max-width: 768px) {
            .header {
                padding: 8px 15px;
            }
            
            .header h1 {
                font-size: 16px;
            }
            
            .header-controls {
                gap: 5px;
            }
            
            .video-source-switch,
            .file-input-btn {
                padding: 4px 8px;
                font-size: 11px;
            }
            
            .settings-btn {
                width: 28px;
                height: 28px;
                font-size: 14px;
            }
            
            .video-container {
                height: 78vh;
                margin: 5px;
            }
            
            .video-controls {
                bottom: 70px;
                padding: 8px 15px;
                gap: 10px;
            }
            
            .video-progress {
                width: 120px;
            }
            
            .video-time {
                font-size: 11px;
                min-width: 60px;
            }
            
            .video-list {
                top: 5px;
                right: 5px;
                max-width: 200px;
                padding: 8px;
            }
            
            .video-list-item {
                padding: 6px 10px;
                font-size: 12px;
            }
            
            .voice-btn {
                width: 60px;
                height: 60px;
                font-size: 24px;
            }
            
            .tts-stop-btn {
                width: 40px;
                height: 40px;
                font-size: 16px;
                margin-left: 8px;
            }
            
            .message {
                font-size: 15px;
                max-width: 85%;
            }
            
            .settings-panel {
                width: 90%;
            }
            
            .chat-container {
                width: 280px;
                right: -280px;
            }
            
            .chat-container.open {
                right: 0;
            }
            
            .chat-toggle.open {
                right: 280px;
            }
        }

        /* 横屏模式 */
        @media (orientation: landscape) and (max-height: 500px) {
            .video-container {
                height: 30vh;
                min-height: 150px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🤖 智能视觉助手</h1>
        <div class="header-controls">
            <button class="video-source-switch camera" id="sourceSwitchBtn">📷 摄像头</button>
            <button class="file-input-btn" id="fileInputBtn">📁 选择视频</button>
            <input type="file" id="videoFileInput" accept="video/*" multiple style="display: none;">
            <button class="settings-btn" id="settingsBtn">⚙️</button>
        </div>
    </div>

    <div class="main-content">
        <div class="video-container">
            <video id="videoFeed" autoplay playsinline muted></video>
            <canvas id="displayCanvas"></canvas>
            <canvas id="captureCanvas" class="hidden"></canvas>
            <div class="camera-status" id="cameraStatus">摄像头连接中...</div>
            
            <!-- 视频控制面板 -->
            <div class="video-controls" id="videoControls">
                <button class="play-pause-btn" id="playPauseBtn">⏸️</button>
                <div class="video-progress" id="videoProgress">
                    <div class="video-progress-bar" id="progressBar"></div>
                </div>
                <div class="video-time" id="videoTime">00:00 / 00:00</div>
            </div>
            
            <!-- 视频文件列表 -->
            <div class="video-list" id="videoList">
                <div style="color: white; font-size: 14px; margin-bottom: 10px; text-align: center;">📁 视频列表</div>
            </div>
            
            <div class="controls-overlay">
                <div class="ai-status" id="aiStatus">AI准备就绪</div>
                <button class="voice-btn idle" id="voiceBtn">🎤</button>
                <button class="tts-stop-btn" id="mainTtsStopBtn" style="display: none;">🔇</button>
            </div>
        </div>

        <div class="chat-toggle" id="chatToggle">💬</div>
        <div class="chat-container" id="chatContainer">
            <div class="chat-messages" id="chatMessages">
                <div class="message system">👋 您好！我是您的智能视觉助手，按住麦克风按钮说话，我会根据摄像头看到的内容回答您的问题。</div>
            </div>

            <div class="input-container">
                <div class="status-text" id="statusText">按住说话开始对话</div>
            </div>
        </div>
    </div>

    <!-- 设置面板 -->
    <div class="overlay" id="overlay"></div>
    <div class="settings-panel" id="settingsPanel">
        <button class="close-settings" id="closeSettings">&times;</button>
        <h3>设置</h3>
        
        <div class="setting-group">
            <label for="apiUrl">API 地址:</label>
            <input type="text" id="apiUrl" value="http://27.159.93.61:8199">
            <!-- <input type="text" id="apiUrl" value="http://203.176.94.21:18097"> -->
        </div>
        
        <div class="setting-group">
            <label for="ttsUrl">TTS 地址:</label>
            <input type="text" id="ttsUrl" value="http://127.0.0.1:8000">
        </div>
        
        <div class="setting-group">
            <label for="voiceLang">语音识别语言:</label>
            <select id="voiceLang">
                <option value="zh-CN">中文</option>
                <option value="en-US">English</option>
            </select>
        </div>
        
        <div class="setting-group">
            <label for="autoTTS">自动语音播报:</label>
            <select id="autoTTS">
                <option value="true">开启</option>
                <option value="false">关闭</option>
            </select>
        </div>
        
        <div class="setting-group">
            <label for="ttsVoice">TTS音色:</label>
            <select id="ttsVoice">
                <option value="">正在加载音色...</option>
            </select>
            <div id="ttsStatus" style="margin-top: 4px; font-size: 11px; color: #666;"></div>
            <div style="margin-top: 8px; display: flex; gap: 8px;">
                <button id="ttsTestBtn" style="flex: 1; padding: 6px 12px; background: #007AFF; color: white; border: none; border-radius: 6px; font-size: 12px; cursor: pointer;">试听</button>
                <button id="ttsStopBtn" style="flex: 1; padding: 6px 12px; background: #FF3B30; color: white; border: none; border-radius: 6px; font-size: 12px; cursor: pointer;">停止</button>
            </div>
            <div style="margin-top: 8px;">
                <button id="ttsRefreshBtn" style="width: 100%; padding: 6px 12px; background: #34C759; color: white; border: none; border-radius: 6px; font-size: 12px; cursor: pointer;">🔄 刷新音色列表</button>
            </div>
        </div>
    </div>

    <script>
        class VoiceChat {
            constructor() {
                this.video = document.getElementById('videoFeed');
                this.displayCanvas = document.getElementById('displayCanvas');
                this.captureCanvas = document.getElementById('captureCanvas');
                this.chatMessages = document.getElementById('chatMessages');
                this.voiceBtn = document.getElementById('voiceBtn');
                this.statusText = document.getElementById('statusText');
                this.cameraStatus = document.getElementById('cameraStatus');
                this.aiStatus = document.getElementById('aiStatus');
                this.chatToggle = document.getElementById('chatToggle');
                this.chatContainer = document.getElementById('chatContainer');
                
                // 视频控制相关元素
                this.videoControls = document.getElementById('videoControls');
                this.playPauseBtn = document.getElementById('playPauseBtn');
                this.videoProgress = document.getElementById('videoProgress');
                this.progressBar = document.getElementById('progressBar');
                this.videoTime = document.getElementById('videoTime');
                this.videoList = document.getElementById('videoList');
                this.sourceSwitchBtn = document.getElementById('sourceSwitchBtn');
                this.fileInputBtn = document.getElementById('fileInputBtn');
                this.videoFileInput = document.getElementById('videoFileInput');
                
                this.stream = null;
                this.recognition = null;
                this.isRecording = false;
                this.isProcessing = false;
                this.microphoneStream = null;
                
                // 视频相关状态
                this.isUsingCamera = true;
                this.videoFiles = [];
                this.currentVideoIndex = -1;
                this.isVideoPlaying = false;
                
                // TTS相关状态
                this.currentUtterance = null;
                this.isSpeaking = false;
                this.voices = [];
                
                this.initEventListeners();
                this.initCamera();
                this.initSpeechRecognition();
                this.initTTSVoices();
                // 无论摄像头是否可用，都开始显示更新循环
                this.updateDisplay();
            }

            initEventListeners() {
                // 语音按钮事件
                this.voiceBtn.addEventListener('touchstart', (e) => {
                    e.preventDefault();
                    this.startRecording();
                });
                
                this.voiceBtn.addEventListener('touchend', (e) => {
                    e.preventDefault();
                    this.stopRecording();
                });
                
                this.voiceBtn.addEventListener('mousedown', (e) => {
                    e.preventDefault();
                    this.startRecording();
                });
                
                this.voiceBtn.addEventListener('mouseup', (e) => {
                    e.preventDefault();
                    this.stopRecording();
                });

                // 设置面板事件
                document.getElementById('settingsBtn').addEventListener('click', () => {
                    this.openSettings();
                });
                
                document.getElementById('closeSettings').addEventListener('click', () => {
                    this.closeSettings();
                });
                
                document.getElementById('overlay').addEventListener('click', () => {
                    this.closeSettings();
                });
                
                // TTS控制事件
                document.getElementById('ttsTestBtn').addEventListener('click', () => {
                    this.testTTSVoice();
                });
                
                document.getElementById('ttsStopBtn').addEventListener('click', () => {
                    this.stopTTS();
                });
                
                document.getElementById('mainTtsStopBtn').addEventListener('click', () => {
                    this.stopTTS();
                });
                
                document.getElementById('ttsVoice').addEventListener('change', () => {
                    this.testTTSVoice();
                });
                
                document.getElementById('ttsRefreshBtn').addEventListener('click', () => {
                    console.log('🔄 手动刷新音色列表');
                    this.initTTSVoices();
                });

                // 聊天切换按钮事件
                this.chatToggle.addEventListener('click', () => {
                    this.toggleChat();
                });

                // 视频源切换按钮事件
                this.sourceSwitchBtn.addEventListener('click', () => {
                    this.toggleVideoSource();
                });

                // 文件选择按钮事件
                this.fileInputBtn.addEventListener('click', () => {
                    this.videoFileInput.click();
                });

                // 文件输入事件
                this.videoFileInput.addEventListener('change', (e) => {
                    this.handleFileSelection(e.target.files);
                });

                // 视频播放控制事件
                this.playPauseBtn.addEventListener('click', () => {
                    this.togglePlayPause();
                });

                // 进度条点击事件
                this.videoProgress.addEventListener('click', (e) => {
                    this.handleProgressClick(e);
                });

                // 视频时间更新事件
                this.video.addEventListener('timeupdate', () => {
                    this.updateProgress();
                });

                // 视频结束事件
                this.video.addEventListener('ended', () => {
                    this.onVideoEnded();
                });

                // 视频加载完成事件
                this.video.addEventListener('loadedmetadata', () => {
                    this.onVideoLoaded();
                });

                // 防止页面滚动
                document.addEventListener('touchmove', (e) => {
                    if (e.target === this.voiceBtn) {
                        e.preventDefault();
                    }
                }, { passive: false });
            }

            async initCamera() {
                try {
                    // 首先尝试后置摄像头，如果失败则使用前置摄像头
                    let constraints = { 
                        video: { 
                            facingMode: 'environment' // 后置摄像头
                        }, 
                        audio: false 
                    };
                    
                    try {
                        this.stream = await navigator.mediaDevices.getUserMedia(constraints);
                    } catch (backCameraError) {
                        console.log('后置摄像头不可用，尝试前置摄像头');
                        constraints.video.facingMode = 'user';
                        this.stream = await navigator.mediaDevices.getUserMedia(constraints);
                    }
                    
                    this.video.srcObject = this.stream;
                    
                    // 等待视频元数据加载完成
                    await new Promise((resolve) => {
                        this.video.addEventListener('loadedmetadata', () => {
                            console.log('视频尺寸:', this.video.videoWidth, 'x', this.video.videoHeight);
                            this.displayCanvas.width = this.video.videoWidth;
                            this.displayCanvas.height = this.video.videoHeight;
                            this.cameraStatus.textContent = '摄像头已连接';
                            this.cameraStatus.style.background = 'rgba(52, 199, 89, 0.8)';
                            resolve();
                        }, { once: true });
                    });
                    
                    // 等待视频开始播放
                    await new Promise((resolve) => {
                        this.video.addEventListener('canplay', () => {
                            console.log('视频可以播放了');
                            this.video.play().then(() => {
                                console.log('视频开始播放');
                                resolve();
                            }).catch(err => {
                                console.error('视频播放失败:', err);
                                resolve();
                            });
                        }, { once: true });
                    });
                    
                } catch (err) {
                    console.log("摄像头不可用:", err);
                    this.stream = null;
                    this.cameraStatus.textContent = '请选择视频文件或连接摄像头';
                    this.cameraStatus.style.background = 'rgba(255, 149, 0, 0.8)';
                    // 不显示错误消息，而是提示用户选择视频文件
                    this.addMessage('system', '未检测到摄像头，请选择视频文件进行体验。');
                    // 禁用摄像头切换按钮，引导用户选择视频
                    this.sourceSwitchBtn.style.opacity = '0.5';
                    this.sourceSwitchBtn.disabled = true;
                }
            }

            async initSpeechRecognition() {
                // 先获取麦克风权限
                try {
                    this.microphoneStream = await navigator.mediaDevices.getUserMedia({ audio: true });
                    console.log('麦克风权限获取成功');
                } catch (err) {
                    console.error('麦克风权限获取失败:', err);
                    this.addMessage('system', '麦克风权限获取失败，语音功能将不可用。');
                    return;
                }

                if ('webkitSpeechRecognition' in window) {
                    this.recognition = new webkitSpeechRecognition();
                } else if ('SpeechRecognition' in window) {
                    this.recognition = new SpeechRecognition();
                } else {
                    this.addMessage('system', '浏览器不支持语音识别功能。');
                    return;
                }

                this.recognition.continuous = false;
                this.recognition.interimResults = false;
                this.recognition.lang = document.getElementById('voiceLang').value || 'zh-CN';

                this.recognition.onresult = (event) => {
                    const transcript = event.results[0][0].transcript;
                    this.addMessage('user', transcript);
                    this.processQuestion(transcript);
                };

                this.recognition.onerror = (event) => {
                    console.error('语音识别错误:', event.error);
                    this.addMessage('system', `语音识别错误: ${event.error}`);
                    this.resetRecordingState();
                };

                this.recognition.onend = () => {
                    this.resetRecordingState();
                };
            }

            initTTSVoices() {
                let loadAttempts = 0;
                const maxAttempts = 50; // 最多尝试5秒
                
                // 等待语音列表加载
                const loadVoices = () => {
                    loadAttempts++;
                    const voices = speechSynthesis.getVoices();
                    this.voices = voices; // 缓存音色列表
                    const voiceSelect = document.getElementById('ttsVoice');
                    const statusDiv = document.getElementById('ttsStatus');
                    
                    console.log(`TTS音色加载尝试 ${loadAttempts}/${maxAttempts}, 找到 ${voices.length} 个音色`);
                    
                    // 更新加载状态
                    if (voices.length === 0 && loadAttempts < maxAttempts) {
                        statusDiv.textContent = `正在加载音色... (${loadAttempts}/${maxAttempts})`;
                        statusDiv.style.color = '#FF9500';
                        setTimeout(loadVoices, 100);
                        return;
                    }
                    
                    // 清空选项
                    voiceSelect.innerHTML = '';
                    
                    if (voices.length === 0) {
                        console.warn('未找到任何TTS音色，使用系统默认');
                        const defaultOption = document.createElement('option');
                        defaultOption.value = '';
                        defaultOption.textContent = '系统默认';
                        voiceSelect.appendChild(defaultOption);
                        return;
                    }
                    
                    // 打印所有可用音色用于调试
                    console.log('所有可用音色:', voices.map(v => `${v.name} (${v.lang})`));
                    
                    // 过滤中文音色，优先显示中文音色
                    const chineseVoices = voices.filter(voice => 
                        voice.lang.toLowerCase().includes('zh') || 
                        voice.lang.toLowerCase().includes('cn') || 
                        voice.name.toLowerCase().includes('chinese') || 
                        voice.name.includes('中文') ||
                        voice.name.toLowerCase().includes('mandarin') ||
                        voice.lang.startsWith('zh')
                    );
                    
                    console.log('筛选的中文音色:', chineseVoices.map(v => `${v.name} (${v.lang})`));
                    
                    // 添加中文音色选项
                    chineseVoices.forEach((voice, index) => {
                        const option = document.createElement('option');
                        option.value = voice.name;
                        option.textContent = `${voice.name} (${voice.lang})`;
                        if (index === 0) option.selected = true;
                        voiceSelect.appendChild(option);
                    });
                    
                    // 如果没有中文音色，添加所有音色
                    if (chineseVoices.length === 0) {
                        console.log('未找到中文音色，显示所有音色');
                        voices.forEach((voice, index) => {
                            const option = document.createElement('option');
                            option.value = voice.name;
                            option.textContent = `${voice.name} (${voice.lang})`;
                            if (index === 0) option.selected = true;
                            voiceSelect.appendChild(option);
                        });
                    } else {
                        // 如果有其他语言的音色，也可以添加（可选）
                        const otherVoices = voices.filter(voice => 
                            !voice.lang.toLowerCase().includes('zh') && 
                            !voice.lang.toLowerCase().includes('cn') && 
                            !voice.name.toLowerCase().includes('chinese') && 
                            !voice.name.includes('中文') &&
                            !voice.name.toLowerCase().includes('mandarin') &&
                            !voice.lang.startsWith('zh')
                        );
                        
                        if (otherVoices.length > 0) {
                            // 添加分隔符
                            const separator = document.createElement('option');
                            separator.disabled = true;
                            separator.textContent = '--- 其他语言 ---';
                            voiceSelect.appendChild(separator);
                            
                            // 添加其他音色（限制数量避免列表过长）
                            otherVoices.slice(0, 8).forEach(voice => {
                                const option = document.createElement('option');
                                option.value = voice.name;
                                option.textContent = `${voice.name} (${voice.lang})`;
                                voiceSelect.appendChild(option);
                            });
                        }
                    }
                    
                    console.log(`✅ TTS音色加载完成: 总共 ${voices.length} 个音色，中文音色 ${chineseVoices.length} 个`);
                    
                    // 更新状态显示
                    if (voices.length > 0) {
                        statusDiv.textContent = `已加载 ${voices.length} 个音色`;
                        statusDiv.style.color = '#34C759';
                    } else {
                        statusDiv.textContent = '未找到可用音色';
                        statusDiv.style.color = '#FF3B30';
                    }
                    
                    // 立即测试当前选中的音色
                    if (voiceSelect.value) {
                        console.log('默认选中音色:', voiceSelect.value);
                        
                        // 验证音色是否可用
                        setTimeout(() => {
                            this.validateSelectedVoice();
                        }, 200);
                    }
                };
                
                // 语音列表变化时重新加载
                speechSynthesis.addEventListener('voiceschanged', loadVoices);
                
                // 立即尝试加载一次
                loadVoices();
                
                // 额外的延迟加载确保兼容性
                setTimeout(loadVoices, 500);
                setTimeout(loadVoices, 1000);
            }

            validateSelectedVoice() {
                const selectedVoice = document.getElementById('ttsVoice').value;
                if (!selectedVoice) return;
                
                const voices = speechSynthesis.getVoices();
                const voice = voices.find(v => v.name === selectedVoice);
                
                if (voice) {
                    console.log('✅ 音色验证通过:', voice.name, '- 语言:', voice.lang);
                } else {
                    console.error('❌ 音色验证失败 - 未找到指定音色:', selectedVoice);
                    console.log('当前可用音色:', voices.map(v => v.name));
                }
            }

            checkTTSFunction() {
                console.log('🧪 开始TTS功能全面检查...');
                
                // 检查浏览器支持
                if (!('speechSynthesis' in window)) {
                    console.error('❌ 浏览器不支持Web Speech API');
                    return;
                }
                
                console.log('✅ 浏览器支持Web Speech API');
                
                // 检查音色列表
                const voices = speechSynthesis.getVoices();
                console.log('📊 可用音色数量:', voices.length);
                
                if (voices.length === 0) {
                    console.warn('⚠️ 当前没有可用音色，尝试重新加载...');
                    this.initTTSVoices();
                    return;
                }
                
                // 检查当前选中的音色
                const voiceSelect = document.getElementById('ttsVoice');
                const selectedVoice = voiceSelect.value;
                
                console.log('🎯 当前选中音色:', selectedVoice);
                console.log('📝 音色选择框选项数量:', voiceSelect.options.length);
                
                if (selectedVoice) {
                    const voice = voices.find(v => v.name === selectedVoice);
                    if (voice) {
                        console.log('✅ TTS功能检查完成 - 一切正常');
                        console.log('📍 音色详情:', {
                            name: voice.name,
                            lang: voice.lang,
                            localService: voice.localService,
                            default: voice.default
                        });
                    } else {
                        console.error('❌ 音色不匹配，尝试修复...');
                        this.initTTSVoices();
                    }
                } else {
                    console.warn('⚠️ 没有选中音色，尝试设置默认音色...');
                    this.initTTSVoices();
                }
            }

            testTTSVoice() {
                const selectedVoice = document.getElementById('ttsVoice').value;
                console.log('🧪 开始音色测试:', selectedVoice);
                
                // 根据音色语言选择合适的测试文本
                const voices = speechSynthesis.getVoices();
                const voice = voices.find(v => v.name === selectedVoice);
                
                let testText = '您好，这是TTS音色试听测试。';
                if (voice) {
                    if (voice.lang.toLowerCase().includes('en')) {
                        testText = 'Hello, this is a TTS voice test.';
                    } else if (voice.lang.toLowerCase().includes('zh')) {
                        testText = '您好，这是TTS音色试听测试。音色名称：' + voice.name;
                    } else if (voice.lang.toLowerCase().includes('ja')) {
                        testText = 'こんにちは、これはTTS音声テストです。';
                    } else {
                        testText = 'Hello, this is a TTS voice test for ' + voice.name;
                    }
                }
                
                console.log('🗣️ 测试文本:', testText);
                this.speakText(testText, true);
            }

            stopTTS() {
                if ('speechSynthesis' in window && speechSynthesis.speaking) {
                    speechSynthesis.cancel();
                    this.isSpeaking = false;
                    this.currentUtterance = null;
                    console.log('TTS播报已停止');
                    
                    // 更新状态显示
                    if (this.aiStatus.textContent === '🗣️ 正在播报...') {
                        this.aiStatus.textContent = 'AI准备就绪';
                    }
                    
                    // 隐藏主界面停止按钮
                    document.getElementById('mainTtsStopBtn').style.display = 'none';
                }
            }

            startRecording() {
                if (this.isRecording || this.isProcessing || !this.recognition) return;
                
                this.isRecording = true;
                this.voiceBtn.classList.remove('idle', 'processing');
                this.voiceBtn.classList.add('recording');
                this.statusText.textContent = '正在录音中...';
                this.aiStatus.textContent = '👂 正在聆听...';
                
                this.recognition.lang = document.getElementById('voiceLang').value;
                this.recognition.start();
            }

            stopRecording() {
                if (!this.isRecording) return;
                
                this.recognition.stop();
                this.voiceBtn.classList.remove('recording');
                this.voiceBtn.classList.add('processing');
                this.statusText.textContent = '语音识别中...';
                this.aiStatus.textContent = '🔄 正在处理...';
            }

            resetRecordingState() {
                this.isRecording = false;
                this.isProcessing = false;
                this.voiceBtn.classList.remove('recording', 'processing');
                this.voiceBtn.classList.add('idle');
                this.statusText.textContent = '按住说话开始对话';
                this.aiStatus.textContent = 'AI准备就绪';
            }

            // 视频源切换
            toggleVideoSource() {
                if (this.isUsingCamera) {
                    this.switchToVideoMode();
                } else {
                    this.switchToCameraMode();
                }
            }

            // 切换到视频文件模式
            async switchToVideoMode() {
                if (this.videoFiles.length === 0) {
                    this.addMessage('system', '请先选择视频文件');
                    return;
                }

                this.isUsingCamera = false;
                this.sourceSwitchBtn.textContent = '🎬 视频';
                this.sourceSwitchBtn.classList.remove('camera');
                
                // 停止摄像头
                if (this.stream) {
                    this.stream.getTracks().forEach(track => track.stop());
                    this.stream = null;
                }

                // 加载第一个视频文件
                this.loadVideo(0);
                this.videoControls.classList.add('show');
                this.videoList.classList.add('show');
                this.cameraStatus.textContent = '视频模式';
                this.cameraStatus.style.background = 'rgba(255, 149, 0, 0.8)';
                this.addMessage('system', '🎬 已切换到视频模式，点击播放按钮开始播放，按住麦克风进行视觉询问');
            }

            // 切换到摄像头模式
            async switchToCameraMode() {
                // 如果摄像头按钮被禁用，说明没有摄像头
                if (this.sourceSwitchBtn.disabled) {
                    this.addMessage('system', '未检测到摄像头，无法切换到摄像头模式');
                    return;
                }
                
                this.isUsingCamera = true;
                this.sourceSwitchBtn.textContent = '📷 摄像头';
                this.sourceSwitchBtn.classList.add('camera');
                
                // 停止视频播放
                this.video.pause();
                this.video.src = '';
                
                this.videoControls.classList.remove('show');
                this.videoList.classList.remove('show');
                
                // 重新初始化摄像头
                await this.initCamera();
            }

            // 处理文件选择
            handleFileSelection(files) {
                if (files.length === 0) return;

                this.videoFiles = [];
                const videoList = this.videoList;
                
                // 清除现有列表（保留标题）
                const children = Array.from(videoList.children);
                children.slice(1).forEach(child => child.remove());

                // 添加新文件
                Array.from(files).forEach((file, index) => {
                    if (file.type.startsWith('video/')) {
                        const url = URL.createObjectURL(file);
                        this.videoFiles.push({
                            file: file,
                            url: url,
                            name: file.name
                        });

                        // 创建列表项
                        const listItem = document.createElement('div');
                        listItem.className = 'video-list-item';
                        listItem.textContent = file.name;
                        listItem.addEventListener('click', () => {
                            this.loadVideo(index);
                        });
                        videoList.appendChild(listItem);
                    }
                });

                if (this.videoFiles.length > 0) {
                    this.addMessage('system', `已加载 ${this.videoFiles.length} 个视频文件`);
                    
                    // 如果当前不是摄像头模式，或者没有摄像头，自动切换到视频模式
                    if (!this.isUsingCamera || this.sourceSwitchBtn.disabled) {
                        this.switchToVideoMode();
                    }
                } else {
                    this.addMessage('system', '未找到有效的视频文件');
                }
            }

            // 加载指定视频
            loadVideo(index) {
                if (index < 0 || index >= this.videoFiles.length) return;

                this.currentVideoIndex = index;
                const videoData = this.videoFiles[index];
                
                // 更新列表项的活跃状态
                const listItems = this.videoList.querySelectorAll('.video-list-item');
                listItems.forEach((item, i) => {
                    if (i === index) {
                        item.classList.add('active');
                    } else {
                        item.classList.remove('active');
                    }
                });

                // 设置视频源
                this.video.src = videoData.url;
                this.video.muted = true; // 保持静音以避免自动播放限制
                this.video.load();
                
                this.cameraStatus.textContent = `📹 ${videoData.name}`;
                this.addMessage('system', `正在播放: ${videoData.name}`);
            }

            // 播放/暂停切换
            togglePlayPause() {
                if (!this.isUsingCamera && this.currentVideoIndex >= 0) {
                    if (this.video.paused) {
                        this.video.play();
                        this.playPauseBtn.textContent = '⏸️';
                        this.isVideoPlaying = true;
                    } else {
                        this.video.pause();
                        this.playPauseBtn.textContent = '▶️';
                        this.isVideoPlaying = false;
                    }
                }
            }

            // 处理进度条点击
            handleProgressClick(e) {
                if (!this.isUsingCamera && this.video.duration) {
                    const rect = this.videoProgress.getBoundingClientRect();
                    const pos = (e.clientX - rect.left) / rect.width;
                    this.video.currentTime = pos * this.video.duration;
                }
            }

            // 更新进度条
            updateProgress() {
                if (!this.isUsingCamera && this.video.duration) {
                    const progress = (this.video.currentTime / this.video.duration) * 100;
                    this.progressBar.style.width = progress + '%';
                    
                    const currentTime = this.formatTime(this.video.currentTime);
                    const duration = this.formatTime(this.video.duration);
                    this.videoTime.textContent = `${currentTime} / ${duration}`;
                }
            }

            // 视频结束处理
            onVideoEnded() {
                this.playPauseBtn.textContent = '▶️';
                this.isVideoPlaying = false;
                
                // 自动播放下一个视频
                if (this.currentVideoIndex < this.videoFiles.length - 1) {
                    setTimeout(() => {
                        this.loadVideo(this.currentVideoIndex + 1);
                        setTimeout(() => {
                            this.video.play();
                            this.playPauseBtn.textContent = '⏸️';
                            this.isVideoPlaying = true;
                        }, 500);
                    }, 1000);
                }
            }

            // 视频加载完成处理
            onVideoLoaded() {
                if (!this.isUsingCamera) {
                    this.updateProgress();
                    // 自动开始播放
                    this.video.play().then(() => {
                        this.playPauseBtn.textContent = '⏸️';
                        this.isVideoPlaying = true;
                    }).catch(err => {
                        console.log('自动播放失败，需要用户手动播放:', err);
                        this.playPauseBtn.textContent = '▶️';
                        this.isVideoPlaying = false;
                    });
                }
            }

            // 格式化时间
            formatTime(seconds) {
                const minutes = Math.floor(seconds / 60);
                const secs = Math.floor(seconds % 60);
                return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            }

            updateDisplay() {
                // 检查是否有可用的视频源（摄像头或视频文件）
                const hasVideoSource = (this.isUsingCamera && this.stream) || (!this.isUsingCamera && this.currentVideoIndex >= 0);
                
                if (!hasVideoSource || !this.video.videoWidth || this.video.readyState < 2) {
                    requestAnimationFrame(() => this.updateDisplay());
                    return;
                }
                
                try {
                    const context = this.displayCanvas.getContext('2d');
                    // 确保canvas尺寸正确
                    if (this.displayCanvas.width !== this.video.videoWidth) {
                        this.displayCanvas.width = this.video.videoWidth;
                    }
                    if (this.displayCanvas.height !== this.video.videoHeight) {
                        this.displayCanvas.height = this.video.videoHeight;
                    }
                    
                    // 清除画布
                    context.clearRect(0, 0, this.displayCanvas.width, this.displayCanvas.height);
                    
                    // 绘制视频帧（无论是摄像头还是视频文件）
                    context.drawImage(this.video, 0, 0, this.displayCanvas.width, this.displayCanvas.height);
                } catch (err) {
                    console.error('绘制视频帧失败:', err);
                }
                
                requestAnimationFrame(() => this.updateDisplay());
            }

            captureImage() {
                // 检查是否有可用的视频源（摄像头或视频文件）
                const hasVideoSource = (this.isUsingCamera && this.stream) || (!this.isUsingCamera && this.currentVideoIndex >= 0);
                
                if (!hasVideoSource || !this.video.videoWidth || this.video.readyState < 2) {
                    console.log('无法捕获图像 - 视频源状态:', {
                        isUsingCamera: this.isUsingCamera,
                        hasStream: !!this.stream,
                        currentVideoIndex: this.currentVideoIndex,
                        videoWidth: this.video.videoWidth,
                        readyState: this.video.readyState
                    });
                    return null;
                }
                
                // 在视频模式下，检查视频是否正在播放
                if (!this.isUsingCamera && this.video.paused) {
                    console.log('视频已暂停，无法进行AI分析');
                    return 'paused';
                }
                
                this.captureCanvas.width = this.video.videoWidth;
                this.captureCanvas.height = this.video.videoHeight;
                
                const context = this.captureCanvas.getContext('2d');
                context.drawImage(this.video, 0, 0);
                
                return this.captureCanvas.toDataURL('image/jpeg', 0.8);
            }

            async processQuestion(question) {
                this.isProcessing = true;
                this.statusText.textContent = 'AI正在思考...';
                this.aiStatus.textContent = '🤔 分析画面中...';
                
                const imageData = this.captureImage();
                if (!imageData) {
                    const errorMsg = this.isUsingCamera ? 
                        '无法获取图像，请检查摄像头状态。' : 
                        '无法获取视频画面，请确保视频正在播放。';
                    this.addMessage('system', errorMsg);
                    this.resetRecordingState();
                    return;
                } else if (imageData === 'paused') {
                    this.addMessage('system', '视频已暂停，请点击播放按钮继续播放后再进行提问。');
                    this.resetRecordingState();
                    return;
                }

                try {
                    const apiUrl = document.getElementById('apiUrl').value;
                    
                    // 系统提示词
                    const systemPrompt = `# 角色
你是一个专为中国道路交通场景设计的视觉问答AI助手。

# 核心任务
分析用户提供的实时视频帧图像，并根据图像内容，用简短、明确的中文回答用户的问题。

# 行为准则
1.  **回答简短明确**：直接给出核心答案，不要有任何多余的客套话或解释。例如，不要说"根据图像分析，这辆车是..."或"您好，我看到的树是..."。
2.  **聚焦图像事实**：你的回答必须严格基于图像中的可见信息。如果问题无法根据图像回答（例如，物体太远、被遮挡或模糊），直接回答"无法识别"或"信息不足"。
3.  **中国本地化**：优先使用中国大陆的常用称呼。例如，车辆品牌（如：比亚迪、理想、问界）、植物名称（如：银杏、法桐）、交通标志等。

# 示例
---
**示例 1:**
*   [用户问题]: 前面这辆白色的车是什么牌子？
*   [你的回答]: 特斯拉 Model Y。

**示例 2:**
*   [用户问题]: 右边这个是什么树？
*   [你的回答]: 银杏树。

---

# 开始任务
请严格遵循以上指示。现在，分析以下输入并回答。`;

                    const response = await fetch(`${apiUrl}/v1/chat/completions`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            max_tokens: 200,
                            messages: [
                                {
                                    role: 'system',
                                    content: systemPrompt
                                },
                                { 
                                    role: 'user', 
                                    content: [
                                        { type: 'text', text: question },
                                        { 
                                            type: 'image_url', 
                                            image_url: { url: imageData }
                                        }
                                    ] 
                                }
                            ]
                        })
                    });

                    if (!response.ok) {
                        throw new Error(`服务器错误: ${response.status}`);
                    }

                    const data = await response.json();
                    const answer = data.choices[0].message.content;
                    
                    this.addMessage('assistant', answer);
                    this.aiStatus.textContent = '🗣️ 正在播报...';
                    
                    // 自动TTS播报
                    if (document.getElementById('autoTTS').value === 'true') {
                        await this.speakText(answer);
                    }
                    
                } catch (error) {
                    console.error('处理问题时出错:', error);
                    this.addMessage('system', `出错了: ${error.message}`);
                    this.aiStatus.textContent = '❌ 处理失败';
                }
                
                this.resetRecordingState();
            }

            async speakText(text, isTest = false) {
                try {
                    // 如果正在播放语音，先停止
                    if (this.isSpeaking) {
                        this.stopTTS();
                    }
                    
                    // 首先尝试使用Web Speech API
                    if ('speechSynthesis' in window) {
                        const utterance = new SpeechSynthesisUtterance(text);
                        utterance.lang = 'zh-CN';
                        utterance.rate = 0.9;
                        utterance.pitch = 1.0;
                        utterance.volume = 1.0;
                        
                        // 应用选定的音色
                        const selectedVoice = document.getElementById('ttsVoice').value;
                        console.log('🎵 TTS播放准备 - 选中音色:', selectedVoice);
                        
                        if (selectedVoice) {
                            if (this.voices.length === 0) {
                                console.warn('⚠️ TTS音色列表缓存为空! 尝试同步获取。');
                                this.voices = speechSynthesis.getVoices();
                            }
                            const voices = this.voices;
                            console.log('📋 使用已加载音色数量:', voices.length);
                            
                            const voice = voices.find(v => v.name === selectedVoice);
                            if (voice) {
                                utterance.voice = voice;
                                console.log('✅ 成功应用音色:', voice.name, '语言:', voice.lang, '本地:', voice.localService ? '本地' : '云端');
                            } else {
                                console.warn('⚠️ 未找到指定音色:', selectedVoice);
                                console.log('可用音色列表:', voices.map(v => v.name));
                            }
                        } else {
                            console.log('🔧 使用系统默认音色');
                        }
                        
                        // 设置事件监听器
                        utterance.onstart = () => {
                            this.isSpeaking = true;
                            this.currentUtterance = utterance;
                            if (!isTest) {
                                this.aiStatus.textContent = '🗣️ 正在播报...';
                                document.getElementById('mainTtsStopBtn').style.display = 'flex';
                            }
                            console.log('TTS开始播放');
                        };
                        
                        utterance.onend = () => {
                            this.isSpeaking = false;
                            this.currentUtterance = null;
                            if (!isTest) {
                                this.aiStatus.textContent = 'AI准备就绪';
                                document.getElementById('mainTtsStopBtn').style.display = 'none';
                            }
                            console.log('TTS播放结束');
                        };
                        
                        utterance.onerror = (event) => {
                            console.error('TTS播放错误:', event.error);
                            this.isSpeaking = false;
                            this.currentUtterance = null;
                            if (!isTest) {
                                this.aiStatus.textContent = 'AI准备就绪';
                                document.getElementById('mainTtsStopBtn').style.display = 'none';
                            }
                        };
                        
                        speechSynthesis.speak(utterance);
                        return;
                    }
                    
                    // 如果有TTS服务器，可以在这里调用
                    const ttsUrl = document.getElementById('ttsUrl').value;
                    if (ttsUrl && ttsUrl !== 'http://127.0.0.1:8000') {
                        // 这里可以实现自定义TTS服务调用
                        console.log('调用TTS服务:', ttsUrl);
                    }
                    
                } catch (error) {
                    console.error('语音播报失败:', error);
                    this.isSpeaking = false;
                    this.currentUtterance = null;
                }
            }

            addMessage(type, content) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${type}`;
                messageDiv.textContent = content;
                
                this.chatMessages.appendChild(messageDiv);
                this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
                
                // 如果聊天面板没有打开且不是系统消息，则自动打开
                if (!this.chatContainer.classList.contains('open') && type !== 'system') {
                    setTimeout(() => {
                        this.toggleChat();
                    }, 500);
                }
            }

            openSettings() {
                document.getElementById('overlay').classList.add('show');
                document.getElementById('settingsPanel').classList.add('open');
            }

            closeSettings() {
                document.getElementById('overlay').classList.remove('show');
                document.getElementById('settingsPanel').classList.remove('open');
            }

            toggleChat() {
                const isOpen = this.chatContainer.classList.contains('open');
                if (isOpen) {
                    this.chatContainer.classList.remove('open');
                    this.chatToggle.classList.remove('open');
                    this.chatToggle.textContent = '💬';
                } else {
                    this.chatContainer.classList.add('open');
                    this.chatToggle.classList.add('open');
                    this.chatToggle.textContent = '❌';
                    // 滚动到最新消息
                    setTimeout(() => {
                        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
                    }, 300);
                }
            }
        }

        // 初始化应用
        window.addEventListener('DOMContentLoaded', () => {
            window.voiceChat = new VoiceChat();
            
            // 延迟检查TTS功能
            setTimeout(() => {
                console.log('🔍 执行TTS功能检查...');
                if (window.voiceChat) {
                    window.voiceChat.checkTTSFunction();
                }
            }, 2000);
        });

        // 防止页面意外关闭时释放摄像头、麦克风和视频资源
        window.addEventListener('beforeunload', () => {
            if (window.voiceChat) {
                if (window.voiceChat.stream) {
                    window.voiceChat.stream.getTracks().forEach(track => track.stop());
                }
                if (window.voiceChat.microphoneStream) {
                    window.voiceChat.microphoneStream.getTracks().forEach(track => track.stop());
                }
                // 释放视频文件资源
                if (window.voiceChat.videoFiles) {
                    window.voiceChat.videoFiles.forEach(videoData => {
                        if (videoData.url) {
                            URL.revokeObjectURL(videoData.url);
                        }
                    });
                }
            }
        });
    </script>
</body>
</html>