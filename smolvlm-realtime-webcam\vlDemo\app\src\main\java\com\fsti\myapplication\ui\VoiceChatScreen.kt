package com.fsti.myapplication.ui

import android.content.Context
import android.graphics.Bitmap
import androidx.camera.core.CameraSelector
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.SwitchCamera
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.ContextCompat
import androidx.lifecycle.viewmodel.compose.viewModel
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

@Composable
fun VoiceChatScreen(
    viewModel: MainViewModel = viewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val messages by viewModel.messages.collectAsState()
    val isTtsSpeaking by viewModel.isTtsSpeaking.collectAsState()
    val ttsStatus by viewModel.ttsStatus.collectAsState()
    val lifecycleOwner = LocalLifecycleOwner.current
    val context = LocalContext.current

    // CameraManager setup
    val previewView = remember { PreviewView(context) }
    val cameraManager = remember { CameraManager(context, lifecycleOwner, previewView) }
    
    DisposableEffect(Unit) {
        onDispose {
            cameraManager.shutdown()
        }
    }

    Box(modifier = Modifier.fillMaxSize()) {
        // Camera Preview
        AndroidView({ previewView }, modifier = Modifier.fillMaxSize())

        // Chat messages overlay
        if (messages.isNotEmpty()) {
            LazyColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .fillMaxHeight(0.4f)
                    .align(Alignment.TopCenter)
                    .padding(16.dp)
                    .background(
                        Color.Black.copy(alpha = 0.3f),
                        RoundedCornerShape(16.dp)
                    )
                    .padding(8.dp),
                reverseLayout = true
            ) {
                items(messages.reversed()) { message ->
                    MessageBubble(message)
                }
            }
        }

        // Top-right controls (Camera Switch)
        Row(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(16.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // Camera status indicator
            Surface(
                shape = RoundedCornerShape(16.dp),
                color = Color.Black.copy(alpha = 0.6f)
            ) {
                Text(
                    text = "📷 摄像头已连接",
                    color = Color.White,
                    modifier = Modifier.padding(horizontal = 12.dp, vertical = 6.dp),
                    fontSize = 12.sp
                )
            }

            // Camera switch button
            IconButton(
                onClick = { cameraManager.switchCamera() },
                modifier = Modifier
                    .background(Color.Black.copy(alpha = 0.5f), CircleShape)
            ) {
                Icon(Icons.Default.SwitchCamera, contentDescription = "Switch Camera", tint = Color.White)
            }
        }


        // Bottom controls
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.BottomCenter)
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Status text with TTS status
            Text(
                text = getStatusText(uiState, isTtsSpeaking, ttsStatus),
                color = Color.White,
                modifier = Modifier
                    .background(Color.Black.copy(alpha = 0.5f), RoundedCornerShape(16.dp))
                    .padding(horizontal = 16.dp, vertical = 8.dp)
            )

            Spacer(modifier = Modifier.height(20.dp))

            // Control buttons row
            Row(
                horizontalArrangement = Arrangement.spacedBy(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Voice recording button
                Button(
                    onClick = {
                        if (uiState == UiState.RECORDING) {
                            viewModel.stopRecording()
                            // 录音停止后拍照
                            cameraManager.takePicture { bitmap ->
                               viewModel.onPictureTaken(bitmap)
                            }
                        } else if (uiState == UiState.IDLE) {
                            viewModel.startRecording()
                        }
                    },
                    modifier = Modifier
                        .size(80.dp)
                        .then(
                            if (uiState == UiState.RECORDING) {
                                Modifier.background(
                                    Color(0xFFFF3B30).copy(alpha = 0.2f),
                                    CircleShape
                                )
                            } else Modifier
                        ),
                    shape = CircleShape,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = when (uiState) {
                            UiState.IDLE -> Color(0xFF34C759) // Green
                            UiState.RECORDING -> Color(0xFFFF3B30) // Red
                            UiState.PROCESSING -> Color(0xFFFF9500) // Orange
                        }
                    ),
                    enabled = uiState != UiState.PROCESSING
                ) {
                    Text(
                        text = when (uiState) {
                            UiState.IDLE -> "🎤"
                            UiState.RECORDING -> "⏹️"
                            UiState.PROCESSING -> "⏳"
                        },
                        fontSize = 32.sp
                    )
                }

                // TTS stop button (only show when TTS is speaking)
                if (isTtsSpeaking) {
                    Button(
                        onClick = { viewModel.stopTts() },
                        modifier = Modifier.size(60.dp),
                        shape = CircleShape,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFFFF3B30) // Red
                        )
                    ) {
                        Text(text = "🔇", fontSize = 24.sp)
                    }
                }
            }
        }
    }
}

@Composable
fun MessageBubble(chatMessage: ChatMessage) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = if (chatMessage.isUser) Arrangement.End else Arrangement.Start
    ) {
        Surface(
            shape = RoundedCornerShape(16.dp),
            color = if (chatMessage.isUser) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.surfaceVariant,
            tonalElevation = 2.dp
        ) {
            Text(
                text = chatMessage.message,
                modifier = Modifier.padding(12.dp),
                color = if (chatMessage.isUser) Color.White else MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

private fun getStatusText(
    uiState: UiState,
    isTtsSpeaking: Boolean,
    ttsStatus: TtsManager.TtsStatus
): String {
    return when {
        isTtsSpeaking -> "🗣️ 正在播报..."
        ttsStatus is TtsManager.TtsStatus.Error -> "❌ TTS错误: ${ttsStatus.message}"
        uiState == UiState.RECORDING -> "👂 正在聆听..."
        uiState == UiState.PROCESSING -> "🤔 AI正在思考..."
        else -> "AI准备就绪"
    }
}