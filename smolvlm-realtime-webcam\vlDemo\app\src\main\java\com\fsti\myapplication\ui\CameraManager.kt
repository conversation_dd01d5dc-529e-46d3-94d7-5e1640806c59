package com.fsti.myapplication.ui

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.ImageFormat
import android.graphics.Matrix
import android.graphics.Rect
import android.graphics.YuvImage
import android.util.Log
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import java.io.ByteArrayOutputStream
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

class CameraManager(
    private val context: Context,
    private val lifecycleOwner: LifecycleOwner,
    private val previewView: PreviewView
) {
    private var cameraProvider: ProcessCameraProvider? = null
    private var camera: Camera? = null
    private var preview: Preview? = null
    private var imageCapture: ImageCapture? = null
    private var imageAnalyzer: ImageAnalysis? = null

    private var lensFacing = CameraSelector.LENS_FACING_BACK
    private val cameraExecutor: ExecutorService = Executors.newSingleThreadExecutor()

    private var onPictureTakenCallback: ((Bitmap) -> Unit)? = null

    init {
        startCamera()
    }

    private fun startCamera() {
        val cameraProviderFuture = ProcessCameraProvider.getInstance(context)
        cameraProviderFuture.addListener({
            try {
                cameraProvider = cameraProviderFuture.get()
                bindCameraUseCases()
            } catch (exc: Exception) {
                Log.e(TAG, "Use case binding failed", exc)
            }
        }, ContextCompat.getMainExecutor(context))
    }

    private fun bindCameraUseCases() {
        val cameraProvider = cameraProvider ?: throw IllegalStateException("Camera initialization failed.")

        val cameraSelector = CameraSelector.Builder()
            .requireLensFacing(lensFacing)
            .build()

        // Preview
        preview = Preview.Builder()
            .build()
            .also {
                it.setSurfaceProvider(previewView.surfaceProvider)
            }

        // ImageCapture
        imageCapture = ImageCapture.Builder()
            .setCaptureMode(ImageCapture.CAPTURE_MODE_MINIMIZE_LATENCY)
            .build()

        // ImageAnalysis for real-time frame processing
        imageAnalyzer = ImageAnalysis.Builder()
            .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
            .build()

        try {
            // Unbind use cases before rebinding
            cameraProvider.unbindAll()

            // Bind use cases to camera
            camera = cameraProvider.bindToLifecycle(
                lifecycleOwner,
                cameraSelector,
                preview,
                imageCapture,
                imageAnalyzer
            )

        } catch (exc: Exception) {
            Log.e(TAG, "Use case binding failed", exc)
        }
    }

    fun switchCamera() {
        lensFacing = if (lensFacing == CameraSelector.LENS_FACING_FRONT) {
            CameraSelector.LENS_FACING_BACK
        } else {
            CameraSelector.LENS_FACING_FRONT
        }
        bindCameraUseCases()
    }

    fun takePicture(onPictureTaken: (Bitmap) -> Unit) {
        this.onPictureTakenCallback = onPictureTaken

        val imageCapture = imageCapture ?: run {
            Log.e(TAG, "ImageCapture not initialized")
            return
        }

        try {
            val outputFileOptions = ImageCapture.OutputFileOptions.Builder(
                createTempFile("captured_image", ".jpg", context.cacheDir)
            ).build()

            imageCapture.takePicture(
                outputFileOptions,
                ContextCompat.getMainExecutor(context),
                object : ImageCapture.OnImageSavedCallback {
                    override fun onError(exception: ImageCaptureException) {
                        Log.e(TAG, "Photo capture failed: ${exception.message}", exception)
                        // 可以在这里添加错误回调
                    }

                    override fun onImageSaved(output: ImageCapture.OutputFileResults) {
                        // Convert captured image to bitmap
                        output.savedUri?.let { uri ->
                            try {
                                val inputStream = context.contentResolver.openInputStream(uri)
                                val bitmap = BitmapFactory.decodeStream(inputStream)
                                inputStream?.close()

                                if (bitmap != null) {
                                    // Rotate bitmap if needed based on camera orientation
                                    val rotatedBitmap = rotateBitmapIfNeeded(bitmap)
                                    onPictureTakenCallback?.invoke(rotatedBitmap)

                                    // 清理临时文件
                                    try {
                                        val file = java.io.File(uri.path ?: "")
                                        if (file.exists()) {
                                            file.delete()
                                        }else{}
                                    } catch (e: Exception) {
                                        Log.w(TAG, "Failed to delete temp file", e)
                                    }
                                } else {
                                    Log.e(TAG, "Failed to decode bitmap from captured image")
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "Failed to process captured image", e)
                            }
                        } ?: run {
                            Log.e(TAG, "Captured image URI is null")
                        }
                    }
                }
            )
        } catch (e: Exception) {
            Log.e(TAG, "Failed to take picture", e)
        }
    }

    private fun rotateBitmapIfNeeded(bitmap: Bitmap): Bitmap {
        // For front camera, we might need to flip the image
        if (lensFacing == CameraSelector.LENS_FACING_FRONT) {
            val matrix = Matrix().apply {
                postScale(-1f, 1f, bitmap.width / 2f, bitmap.height / 2f)
            }
            return Bitmap.createBitmap(bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true)
        }
        return bitmap
    }

    fun shutdown() {
        cameraExecutor.shutdown()
        cameraProvider?.unbindAll()
    }

    companion object {
        private const val TAG = "CameraManager"
    }
}