# 智能视觉语音助手 Android 应用

这是一个基于Android的智能视觉语音助手应用，可以通过语音提问并获得基于摄像头画面的AI回答。

## 功能特性

### 🎤 语音识别
- 使用Android原生SpeechRecognizer API
- 支持中文和英文语音识别
- 按住麦克风按钮进行语音输入
- 完整的错误处理和状态管理

### 📷 摄像头控制
- 实时摄像头预览
- 前后摄像头切换功能
- 自动图像捕获和处理
- 支持图像旋转和镜像处理
- 兼容Android API 24+

### 🤖 AI视觉问答
- 与后端多模态大模型API集成
- **专为中国道路交通场景优化的AI助手**
- 内置系统提示词，提供简短明确的回答
- 支持中国本地化内容识别（车辆品牌、植物等）
- 兼容Web端API格式

### 🔊 语音播报与控制
- 使用Android原生TextToSpeech API
- 自动播报AI回答
- **🆕 TTS打断功能** - 可随时停止正在播放的语音
- 实时TTS状态监控
- 支持中文语音合成

### 💬 聊天界面
- 实时显示对话历史
- 用户消息和AI回答分别显示
- 半透明覆盖层设计，不遮挡摄像头预览
- 动态状态指示器（聆听、思考、播报）

## 技术架构

### 核心组件

1. **MainActivity.kt** - 主活动，处理权限管理
2. **VoiceChatScreen.kt** - 主界面Compose UI
3. **MainViewModel.kt** - 业务逻辑和状态管理
4. **CameraManager.kt** - 摄像头管理和图像捕获
5. **SpeechRecognitionManager.kt** - 语音识别管理
6. **TtsManager.kt** - 文字转语音管理
7. **VisionApiClient.kt** - 网络API客户端
8. **ApiModels.kt** - 数据模型定义

### 技术栈

- **UI框架**: Jetpack Compose
- **摄像头**: CameraX
- **网络请求**: OkHttp + Moshi
- **权限管理**: Accompanist Permissions
- **架构模式**: MVVM + StateFlow

## 安装和使用

### 环境要求

- Android Studio Arctic Fox 或更高版本
- Android SDK API 24 (Android 7.0) 或更高版本
- Java 8 或更高版本
- 支持摄像头和麦克风的Android设备

### 编译步骤

1. 克隆项目到本地
2. 使用Android Studio打开vlDemo文件夹
3. 等待Gradle同步完成
4. 连接Android设备或启动模拟器
5. 点击运行按钮编译并安装应用

### 权限配置

应用需要以下权限：
- `CAMERA` - 摄像头访问
- `RECORD_AUDIO` - 麦克风录音
- `INTERNET` - 网络访问
- `MODIFY_AUDIO_SETTINGS` - 音频设置

首次启动时会自动请求这些权限。

### API配置

默认API地址为：`http://203.176.94.21:18099/vl`

如需修改API地址，请在VisionApiClient.kt中更改DEFAULT_API_URL常量。

## 使用说明

1. **启动应用** - 授予摄像头和麦克风权限
2. **语音提问** - 按住麦克风按钮说话，松开后自动识别
3. **获得回答** - AI分析摄像头画面并语音回答问题
4. **切换摄像头** - 点击右上角切换按钮切换前后摄像头
5. **🆕 停止语音播报** - 当AI正在播报时，点击红色🔇按钮可立即停止
6. **查看历史** - 对话历史显示在屏幕上方
7. **状态监控** - 底部状态栏显示当前操作状态（聆听/思考/播报）

### 🚗 专为道路交通场景优化

本应用特别针对中国道路交通场景进行了优化：
- **车辆识别**：准确识别比亚迪、理想、问界等中国品牌
- **植物识别**：识别银杏、法桐等常见城市绿化植物
- **交通标志**：识别中国标准的交通标志和设施
- **简短回答**：直接给出核心答案，无多余解释

## 与Web版本的对比

| 功能 | Web版本 | Android版本 |
|------|---------|-------------|
| 语音识别 | Web Speech API | Android SpeechRecognizer |
| 摄像头控制 | MediaDevices API | CameraX |
| 语音播报 | Web Speech Synthesis | Android TextToSpeech |
| 网络请求 | Fetch API | OkHttp |
| UI框架 | HTML/CSS/JS | Jetpack Compose |
| 平台支持 | 浏览器 | Android设备 |

## 故障排除

### 常见问题

1. **摄像头无法启动**
   - 检查摄像头权限是否已授予
   - 确认设备摄像头功能正常

2. **语音识别失败**
   - 检查麦克风权限是否已授予
   - 确认网络连接正常（语音识别需要网络）

3. **API请求失败**
   - 检查网络连接
   - 确认API服务器地址正确
   - 查看Logcat中的错误信息

### 调试信息

应用使用Android Log系统输出调试信息，可以通过以下标签过滤：
- `CameraManager` - 摄像头相关日志
- `SpeechRecognitionManager` - 语音识别日志
- `VisionApiClient` - API请求日志

## 开发说明

### 项目结构
```
app/src/main/java/com/fsti/myapplication/
├── MainActivity.kt                 # 主活动
├── ui/
│   ├── VoiceChatScreen.kt         # 主界面
│   ├── MainViewModel.kt           # 视图模型
│   ├── CameraManager.kt           # 摄像头管理
│   ├── SpeechRecognitionManager.kt # 语音识别
│   ├── TtsManager.kt              # 语音合成
│   └── theme/                     # UI主题
└── network/
    ├── VisionApiClient.kt         # API客户端
    └── ApiModels.kt               # 数据模型
```

### 扩展功能

可以考虑添加的功能：
- 设置界面（API地址、语言选择等）
- 对话历史保存
- 图像滤镜和处理
- 多语言支持
- 离线语音识别

## 许可证

本项目基于MIT许可证开源。
