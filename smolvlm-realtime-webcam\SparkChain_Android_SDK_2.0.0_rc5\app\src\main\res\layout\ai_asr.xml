<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:focusable="true"
    android:focusableInTouchMode="true">
    <TextView
        android:id="@+id/ai_asr_result_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:textColor="#333333"
        android:textSize="16dp"
        android:text="信息显示："/>

    <TextView
        android:id="@+id/ai_asr_result"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="#333333"
        android:layout_below="@+id/ai_asr_result_info"
        android:layout_above="@+id/ai_asr_language_layout"
        android:scrollbars="vertical"
        android:textSize="16sp"/>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id = "@+id/ai_asr_language_layout"
        android:layout_above="@+id/ai_asr_audio_start_btn"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal"
            >
            <TextView
                android:id="@+id/ai_asr_language_info"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:gravity="center"
                android:text="选择语种："
                android:textColor="#333333"
                android:textSize="15dp" />
            <Spinner
                android:id="@+id/ai_asr_language"
                android:layout_width="wrap_content"
                android:layout_height="match_parent" />

        </LinearLayout>
    </LinearLayout>
    <Button
        android:id="@+id/ai_asr_audio_start_btn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/ai_asr_file_start_btn"
        android:text="麦克风识别"
        android:textColor="#ffffff" />
    <Button
        android:id="@+id/ai_asr_file_start_btn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/ai_asr_stop_btn"
        android:text="文件识别"
        android:textColor="#ffffff" />
    <Button
        android:id="@+id/ai_asr_stop_btn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:text="停止识别"
        android:layout_marginBottom="10dp"
        android:textColor="#ffffff" />
    </RelativeLayout>
