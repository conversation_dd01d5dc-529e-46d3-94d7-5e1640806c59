[versions]
accompanist = "0.32.0"
androidGradlePlugin = "8.2.2"
androidxActivity = "1.8.2"
androidxComposeBom = "2024.02.01"
androidxCore = "1.12.0"
androidxEspresso = "3.5.1"
androidxJunit = "1.1.5"
androidxLifecycle = "2.7.0"
cameraX = "1.3.1"
junit = "4.13.2"
kotlin = "1.9.22"
material3 = "1.2.0"
okhttp = "4.12.0"
moshi = "1.15.0"

[libraries]
accompanist-permissions = { module = "com.google.accompanist:accompanist-permissions", version.ref = "accompanist" }
androidx-activity-compose = { module = "androidx.activity:activity-compose", version.ref = "androidxActivity" }
androidx-camera-camera2 = { module = "androidx.camera:camera-camera2", version.ref = "cameraX" }
androidx-camera-core = { module = "androidx.camera:camera-core", version.ref = "cameraX" }
androidx-camera-lifecycle = { module = "androidx.camera:camera-lifecycle", version.ref = "cameraX" }
androidx-camera-view = { module = "androidx.camera:camera-view", version.ref = "cameraX" }
androidx-compose-bom = { module = "androidx.compose:compose-bom", version.ref = "androidxComposeBom" }
androidx-core-ktx = { module = "androidx.core:core-ktx", version.ref = "androidxCore" }
androidx-espresso-core = { module = "androidx.test.espresso:espresso-core", version.ref = "androidxEspresso" }
androidx-junit = { module = "androidx.test.ext:junit", version.ref = "androidxJunit" }
androidx-lifecycle-runtime-ktx = { module = "androidx.lifecycle:lifecycle-runtime-ktx", version.ref = "androidxLifecycle" }
androidx-lifecycle-viewmodel-compose = { module = "androidx.lifecycle:lifecycle-viewmodel-compose", version.ref = "androidxLifecycle" }
androidx-material3 = { module = "androidx.compose.material3:material3", version.ref = "material3" }
androidx-ui = { module = "androidx.compose.ui:ui" }
androidx-ui-graphics = { module = "androidx.compose.ui:ui-graphics" }
androidx-ui-test-junit4 = { module = "androidx.compose.ui:ui-test-junit4" }
androidx-ui-test-manifest = { module = "androidx.compose.ui:ui-test-manifest" }
androidx-ui-tooling = { module = "androidx.compose.ui:ui-tooling" }
androidx-ui-tooling-preview = { module = "androidx.compose.ui:ui-tooling-preview" }
junit = { module = "junit:junit", version.ref = "junit" }
squareup-okhttp3 = { module = "com.squareup.okhttp3:okhttp", version.ref = "okhttp" }
squareup-moshi = { module = "com.squareup.moshi:moshi", version.ref = "moshi" }
squareup-moshi-kotlin = { module = "com.squareup.moshi:moshi-kotlin", version.ref = "moshi" }

[plugins]
androidApplication = { id = "com.android.application", version.ref = "androidGradlePlugin" }
jetbrainsKotlinAndroid = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }

[bundles]
camerax = ["androidx-camera-core", "androidx-camera-camera2", "androidx-camera-lifecycle", "androidx-camera-view"]

